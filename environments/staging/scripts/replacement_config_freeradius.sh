#!/bin/bash
#
#set -u

source ./.env
source "./${PATH_ENV}/.env"

filename=environments/staging/replacement_files/freeradius/mods-available/sql
filename_temp=environments/staging/replacement_files/freeradius/mods-available/sql_temp
echo "Make replacements in file: $filename"
cat $filename | sed "s/server = \"localhost\"/server = \"$DB_HOST\"/g" > $filename_temp && mv $filename_temp $filename
#cat $filename | sed "s/radius_db = \"rd\"/radius_db = \"$DB_DATABASE\"/g" > $filename_temp && mv $filename_temp $filename
cat $filename | sed "s/login = \"rd\"/login = \"$DB_USER\"/g" > $filename_temp && mv $filename_temp $filename
cat $filename | sed "s/password = \"rd\"/password = \"$DB_PASSWORD\"/g" > $filename_temp && mv $filename_temp $filename

filename=environments/staging/replacement_files/freeradius/mods-enabled/sql
filename_temp=environments/staging/replacement_files/freeradius/mods-enabled/sql_temp
echo "Make replacements in file: $filename"
cat $filename | sed "s/server = \"localhost\"/server = \"$DB_HOST\"/g" > $filename_temp && mv $filename_temp $filename
#cat $filename | sed "s/radius_db = \"rd\"/radius_db = \"$DB_DATABASE\"/g" > $filename_temp && mv $filename_temp $filename
cat $filename | sed "s/login = \"rd\"/login = \"$DB_USER\"/g" > $filename_temp && mv $filename_temp $filename
cat $filename | sed "s/password = \"rd\"/password = \"$DB_PASSWORD\"/g" > $filename_temp && mv $filename_temp $filename

filename=environments/staging/replacement_files/freeradius/mods-config/perl/fup.pl
filename_temp=environments/staging/replacement_files/freeradius/mods-config/perl/fup_temp.pl
echo "Make replacements in file: $filename"
cat $filename | sed "s/\$conf{'db_host'}     = \"127.0.0.1\";/\$conf{'db_host'}     = \"$DB_HOST\";/g" > $filename_temp && mv $filename_temp $filename
#cat $filename | sed "s/\$conf{'db_name'}     = \"rd\";/\$conf{'db_name'}     = \"$DB_DATABASE\";/g" > $filename_temp && mv $filename_temp $filename
cat $filename | sed "s/\$conf{'db_user'}     = \"rd\";/\$conf{'db_user'}     = \"$DB_USER\";/g" > $filename_temp && mv $filename_temp $filename
cat $filename | sed "s/\$conf{'db_passwd'}   = \"rd\";/\$conf{'db_passwd'}   = \"$DB_PASSWORD\";/g" > $filename_temp && mv $filename_temp $filename

filename=environments/staging/replacement_files/freeradius/mods-config/perl/SQLConnector.pm
filename_temp=environments/staging/replacement_files/freeradius/mods-config/perl/SQLConnector_temp.pm
echo "Make replacements in file: $filename"
cat $filename | sed "s/my \$db_server               = '127.0.0.1';/my \$db_server               = '$DB_HOST';/g" > $filename_temp && mv $filename_temp $filename
#cat $filename | sed "s/my \$db_name                 = 'rd';/my \$db_name                 = '$DB_DATABASE';/g" > $filename_temp && mv $filename_temp $filename
cat $filename | sed "s/my \$db_user                 = 'rd';/my \$db_user                 = '$DB_USER';/g" > $filename_temp && mv $filename_temp $filename
cat $filename | sed "s/my \$db_password             = 'rd';/my \$db_password             = '$DB_PASSWORD';/g" > $filename_temp && mv $filename_temp $filename

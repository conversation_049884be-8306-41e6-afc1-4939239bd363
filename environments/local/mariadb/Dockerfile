FROM docker.io/bitnami/mariadb:10.5
RUN mkdir -p /tmp/init-db

COPY conf/db/ /tmp/init-db/
RUN mv /tmp/init-db/my_custom.cnf /opt/bitnami/mariadb/conf/my_custom.cnf

USER root
RUN chmod +x /tmp/init-db/db_priveleges.sql
RUN chmod +x /tmp/init-db/run_seed.sh
RUN chmod +x /tmp/init-db/db_startup.sh
#RUN sed -i "s/'rd'@'%' IDENTIFIED BY 'rd'/'${MARIADB_USER}'@'%' IDENTIFIED BY '${MARIADB_PASSWORD}'/g" /tmp/init-db/db_priveleges.sql
USER 1001

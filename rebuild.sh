#!/bin/bash

if [ ! -f ".env" ]
then
   echo ".env file is missing."
else
    source ./.env
fi

cd "${PATH_ENV}" || exit

if [ $APP_ENV = "staging" ]
then
    bash environments/staging/scripts/copy-replacement-files.sh /dev/null 2>&1
fi

echo
echo "Down all containers..."
docker compose down

echo
echo "Rebuild all containers..."
docker compose build --no-cache

echo
echo "Start all containers..."
docker compose up -d

echo
echo All containers UP!

<?php

namespace App\Console\Commands\Import;

class ImportAlertsCommand extends BaseImportCommand
{
    protected $signature = 'import:alerts
                            {--dry-run : Preview changes without importing}
                            {--connection=mysql : Database connection to use}
                            {--skip-truncate : Skip truncating target table}';

    protected $description = 'Import alerts table (librenms_alerts → alerts)';

    protected function getSourceTable(): string
    {
        return 'librenms_alerts';
    }

    protected function getTargetTable(): string
    {
        return 'alerts';
    }

    protected function getColumnMapping(): array
    {
        // Use manual mapping for alerts
        return [
            'id' => 'id',
            'device_id' => 'device_id',
            'rule_id' => 'rule_id',
            'state' => 'state',
            'alerted' => 'alerted',
            'open' => 'open',
            'note' => 'note',
            'timestamp' => 'timestamp',
            'info' => 'info',
        ];
    }
}

{"name": "cakephp/app", "description": "CakePHP skeleton app", "homepage": "https://cakephp.org", "type": "project", "license": "MIT", "require": {"php": ">=8.2", "ben-menking/routeros-api": "^1.6", "cakephp/authentication": "^2.0", "cakephp/authorization": "^2.0", "cakephp/cakephp": "4.5.*", "cakephp/migrations": "^3.2", "cakephp/plugin-installer": "^1.3", "dapphp/radius": "^3.0", "endroid/qr-code": "^5.0", "evilfreelancer/routeros-api-php": "^1.4", "friendsofcake/cakephp-csvview": "^4.0", "geoip2/geoip2": "^2.12", "guzzlehttp/guzzle": "^7.0", "hybridauth/hybridauth": "~3.0", "mobiledetect/mobiledetectlib": "^2.8", "sprintcube/cakephp-sendgrid": "^4.0", "tecnickcom/tcpdf": "^6.5", "twilio/sdk": "^8.0"}, "require-dev": {"cakephp/bake": "^2.6", "cakephp/cakephp-codesniffer": "^4.5", "cakephp/debug_kit": "^4.5", "josegonzalez/dotenv": "^3.2", "phpunit/phpunit": "~8.5.0 || ^9.3"}, "suggest": {"markstory/asset_compress": "An asset compression plugin which provides file concatenation and a flexible filter system for preprocessing and minification.", "dereuromark/cakephp-ide-helper": "After baking your code, this keeps your annotations in sync with the code evolving from there on for maximum IDE and PHPStan/Psalm compatibility.", "phpstan/phpstan": "PHPStan focuses on finding errors in your code without actually running it. It catches whole classes of bugs even before you write tests for the code.", "cakephp/repl": "Console tools for a REPL interface for CakePHP applications."}, "autoload": {"psr-4": {"App\\": "src/", "Arra\\": "plugins/Arra/src/"}}, "autoload-dev": {"psr-4": {"App\\Test\\": "tests/", "Cake\\Test\\": "vendor/cakephp/cakephp/tests/", "Arra\\Test\\": "plugins/Arra/tests/"}}, "scripts": {"post-install-cmd": "App\\Console\\Installer::postInstall", "post-create-project-cmd": "App\\Console\\Installer::postInstall", "check": ["@test", "@cs-check"], "cs-check": "phpcs --colors -p  src/ tests/", "cs-fix": "phpcbf --colors -p src/ tests/", "stan": "phpstan analyse", "test": "phpunit --colors=always"}, "prefer-stable": true, "config": {"sort-packages": true, "platform-check": false, "allow-plugins": {"cakephp/plugin-installer": true, "dealerdirect/phpcodesniffer-composer-installer": true}}}